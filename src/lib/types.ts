// lib/types.ts
import { z } from "zod";

// Contact form schema with validation rules
export const contactFormSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name must be less than 100 characters")
    .regex(
      /^[a-zA-Z\s'-]+$/,
      "Name can only contain letters, spaces, hyphens, and apostrophes"
    ),

  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(254, "Email must be less than 254 characters"),

  message: z
    .string()
    .min(1, "Message is required")
    .min(10, "Message must be at least 10 characters")
    .max(1000, "Message must be less than 1000 characters")
    .trim(),

  // Optional fields for better lead qualification
  company: z.string().max(200).optional(),
  jobTitle: z.string().max(150).optional(),
  phone: z.string().max(20).optional(),
  website: z.string().url().optional().or(z.literal("")),

  // UTM parameters (will be auto-populated)
  utmSource: z.string().optional(),
  utmMedium: z.string().optional(),
  utmCampaign: z.string().optional(),
  utmContent: z.string().optional(),
  utmTerm: z.string().optional(),
});

// Enhanced lead form for landing pages
export const leadFormSchema = z.object({
  firstName: z
    .string()
    .min(1, "First name is required")
    .max(100, "First name must be less than 100 characters"),

  lastName: z
    .string()
    .min(1, "Last name is required")
    .max(100, "Last name must be less than 100 characters"),

  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(254, "Email must be less than 254 characters"),

  company: z
    .string()
    .min(1, "Company is required")
    .max(200, "Company name must be less than 200 characters"),

  jobTitle: z.string().max(150).optional(),
  phone: z.string().max(20).optional(),
  website: z.string().url().optional().or(z.literal("")),

  // Lead magnet or offer type
  leadMagnet: z.string().optional(),
  interests: z.array(z.string()).optional(),
});

// Newsletter signup schema
export const newsletterSchema = z.object({
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(254, "Email must be less than 254 characters"),

  firstName: z.string().max(100).optional(),
  interests: z.array(z.string()).optional(),
});

// Infer TypeScript types from schemas
export type ContactFormData = z.infer<typeof contactFormSchema>;
export type LeadFormData = z.infer<typeof leadFormSchema>;
export type NewsletterData = z.infer<typeof newsletterSchema>;

// Database types matching our schema
export interface Lead {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  company?: string;
  jobTitle?: string;
  website?: string;
  leadSourceId?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmContent?: string;
  utmTerm?: string;
  ipAddress?: string;
  country?: string;
  state?: string;
  city?: string;
  timezone?: string;
  leadScore: number;
  leadQuality: "hot" | "warm" | "cold";
  leadStatus: "new" | "contacted" | "qualified" | "converted" | "lost";
  isVerified: boolean;
  isDuplicate: boolean;
  duplicateOf?: string;
  isDeleted: boolean;
  createdAt: string;
  updatedAt: string;
  lastContactedAt?: string;
  convertedAt?: string;
}

export interface LeadSource {
  id: string;
  name: string;
  type: "form" | "chat" | "api" | "import" | "social";
  url?: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LeadActivity {
  id: string;
  leadId: string;
  activityType: string;
  description?: string;
  metadata?: Record<string, any>;
  performedBy?: string;
  createdAt: string;
}

export interface LeadNote {
  id: string;
  leadId: string;
  note: string;
  createdBy: string;
  isPrivate: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LeadTag {
  id: string;
  name: string;
  color: string;
  createdAt: string;
}

// Analytics types
export interface DailyAnalytics {
  id: string;
  date: string;
  totalLeads: number;
  newLeads: number;
  hotLeads: number;
  warmLeads: number;
  coldLeads: number;
  convertedLeads: number;
  totalRevenue: number;
  topSource?: string;
  createdAt: string;
}

export interface CampaignPerformance {
  id: string;
  campaignName: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  leadsCount: number;
  qualifiedLeadsCount: number;
  convertedLeadsCount: number;
  totalRevenue: number;
  date: string;
  createdAt: string;
}

// Enhanced form submission state types
export interface FormSubmissionResult {
  success: boolean;
  message: string;
  timestamp?: string;
  leadId?: string; // Return the created lead ID
  leadScore?: number;
  leadQuality?: "hot" | "warm" | "cold";
  errors?: Record<string, string[]>;
  n8nResponse?: unknown;
}

// N8N webhook payload types
export interface N8NLeadPayload {
  // Lead data
  email: string;
  firstName?: string;
  lastName?: string;
  name?: string; // For contact forms that use single name field
  phone?: string;
  company?: string;
  jobTitle?: string;
  website?: string;
  message?: string;

  // Source tracking
  leadSource: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmContent?: string;
  utmTerm?: string;

  // Technical metadata
  metadata: {
    source: string;
    formType: "contact" | "lead" | "newsletter" | "chat";
    userAgent?: string;
    ipAddress?: string;
    referrer?: string;
    timestamp: number;
    sessionId: string;
    pageUrl?: string;
  };

  // Optional lead qualification data
  interests?: string[];
  leadMagnet?: string;
}

// N8N webhook response type
export interface N8NWebhookResponse {
  success: boolean;
  message: string;
  leadId?: string;
  leadScore?: number;
  leadQuality?: "hot" | "warm" | "cold";
  isDuplicate?: boolean;
  timestamp?: string;
  [key: string]: unknown;
}

// Dashboard data types
export interface DashboardStats {
  totalLeads: number;
  newLeadsToday: number;
  hotLeads: number;
  warmLeads: number;
  coldLeads: number;
  conversionRate: number;
  avgLeadScore: number;
  topSource: string;
}

export interface LeadsBySource {
  sourceName: string;
  sourceType: string;
  totalLeads: number;
  hotLeads: number;
  convertedLeads: number;
  avgLeadScore: number;
  lastLeadDate: string;
}

export interface RecentActivity {
  id: string;
  leadId: string;
  leadEmail: string;
  leadName?: string;
  activityType: string;
  description?: string;
  createdAt: string;
  leadQuality: "hot" | "warm" | "cold";
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}
