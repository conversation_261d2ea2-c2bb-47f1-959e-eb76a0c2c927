"use client";

import { useChat } from "@ai-sdk/react";

export default function Chat() {
  const { messages, input, handleInputChange, handleSubmit, status, error } =
    useChat({
      api: "/api/chat",
      onError: (error) => {
        console.error("Chat error:", error);
      },
    });

  const isLoading = status === "streaming" || status === "submitted";

  return (
    <div className="mx-auto w-full max-w-md py-24 flex flex-col stretch">
      {/* Error Display */}
      {error && (
        <div className="mb-4 p-2 bg-red-100 text-red-800 rounded">
          Error: {error.message}
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto mb-20">
        {messages.length === 0 && (
          <div className="text-gray-500 text-center">
            <p>Welcome! Try asking me to:</p>
            <ul className="mt-2 text-sm">
              <li>• &quot;Roll a 6-sided die&quot;</li>
              <li>• &quot;Roll a 20-sided die&quot;</li>
              <li>• Ask any question</li>
            </ul>
          </div>
        )}

        {messages.map((m) => (
          <div key={m.id} className="mb-4 p-3 rounded-lg">
            <div
              className={`${
                m.role === "user"
                  ? "bg-blue-100 text-blue-900 ml-8"
                  : "bg-gray-100 text-gray-900 mr-8"
              } p-3 rounded-lg`}
            >
              <div className="font-semibold text-xs mb-1 uppercase tracking-wide">
                {m.role === "user" ? "You" : "AI"}
              </div>
              <div className="whitespace-pre-wrap">{m.content}</div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="bg-gray-100 text-gray-900 mr-8 p-3 rounded-lg">
            <div className="font-semibold text-xs mb-1 uppercase tracking-wide">
              AI
            </div>
            <div className="flex items-center">
              <div className="animate-pulse">Thinking...</div>
            </div>
          </div>
        )}
      </div>

      {/* Input Form */}
      <form
        onSubmit={handleSubmit}
        className="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md p-4"
      >
        <div className="flex gap-2">
          <input
            className="flex-1 p-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            value={input}
            placeholder="Try: 'Roll a 6-sided die'"
            onChange={handleInputChange}
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Send
          </button>
        </div>
      </form>
    </div>
  );
}
