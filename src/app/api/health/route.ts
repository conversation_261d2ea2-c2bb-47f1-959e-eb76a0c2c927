export async function GET() {
  const health = {
    status: "ok",
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || "development",
    version: process.env.npm_package_version || "unknown",
    checks: {
      openrouter_api_key: !!process.env.OPENROUTER_API_KEY,
      redis_url: !!process.env.REDIS_URL,
    },
  };

  const isHealthy = health.checks.openrouter_api_key;

  return new Response(JSON.stringify(health), {
    status: isHealthy ? 200 : 503,
    headers: {
      "Content-Type": "application/json",
      "Cache-Control": "no-cache",
    },
  });
}
