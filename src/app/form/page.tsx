"use client";

import { useActionState, useEffect, useRef } from "react";
import { submitContactForm } from "@/actions/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import type { FormSubmissionResult } from "@/lib/types";
import { AlertCircle, CheckCircle2 } from "lucide-react";

interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  placeholder: string;
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  isTextarea?: boolean;
  rows?: number;
}

function FormField({
  label,
  name,
  type = "text",
  placeholder,
  required = false,
  disabled = false,
  errors = [],
  isTextarea = false,
  rows = 4,
}: FormFieldProps) {
  const hasErrors = errors.length > 0;
  const fieldId = `field-${name}`;

  return (
    <div className="space-y-2">
      <Label htmlFor={fieldId} className="text-sm font-medium">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      {isTextarea ? (
        <Textarea
          id={fieldId}
          name={name}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          rows={rows}
          className={hasErrors ? "border-red-500 focus:border-red-500" : ""}
          aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
        />
      ) : (
        <Input
          //  autoComplete="off"
          id={fieldId}
          type={type}
          name={name}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={hasErrors ? "border-red-500 focus:border-red-500" : ""}
          aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
        />
      )}

      {hasErrors && (
        <div id={`${fieldId}-error`} className="space-y-1">
          {errors.map((error, index) => (
            <p
              key={index}
              className="text-sm text-red-600 flex items-center gap-1"
            >
              <AlertCircle className="h-3 w-3" />
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}

function FormPage() {
  const [state, formAction, isPending] = useActionState<
    FormSubmissionResult | null,
    FormData
  >(submitContactForm, null);

  const formRef = useRef<HTMLFormElement>(null);

  // Show toast when state changes
  useEffect(() => {
    if (state) {
      if (state.success) {
        toast.success("Form submitted successfully!", {
          description: state.message,
          icon: <CheckCircle2 className="h-4 w-4" />,
        });
        // Reset form on success
        formRef.current?.reset();
      } else {
        // Only show toast for non-validation errors
        if (!state.errors) {
          toast.error("Error submitting form", {
            description: state.message,
            icon: <AlertCircle className="h-4 w-4" />,
          });
        }
      }
    }
  }, [state]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-md space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-2">Contact Form</h1>
          <p className="text-gray-600">Get in touch with us</p>
        </div>

        <form ref={formRef} action={formAction} className="space-y-4">
          {/* Show general error message for non-field errors */}
          {state && !state.success && !state.errors && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{state.message}</AlertDescription>
            </Alert>
          )}

          {/* Show validation error summary */}
          {state?.errors && Object.keys(state.errors).length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Please correct the errors below and try again.
              </AlertDescription>
            </Alert>
          )}

          <FormField
            label="Name"
            name="name"
            placeholder="Your full name"
            required
            disabled={isPending}
            errors={state?.errors?.name}
          />

          <FormField
            label="Email"
            name="email"
            type="email"
            placeholder="<EMAIL>"
            required
            disabled={isPending}
            errors={state?.errors?.email}
          />

          <FormField
            label="Message"
            name="message"
            placeholder="Tell us how we can help you..."
            required
            disabled={isPending}
            errors={state?.errors?.message}
            isTextarea
            rows={4}
          />

          <Button type="submit" disabled={isPending} className="w-full">
            {isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                Sending...
              </>
            ) : (
              "Send Message"
            )}
          </Button>
        </form>
      </div>
    </div>
  );
}

export default FormPage;
