"use client";

import { useActionState, useEffect, useRef } from "react";
import { submitNewsletterForm } from "@/actions/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import type { FormSubmissionResult } from "@/lib/types";
import { AlertCircle, CheckCircle2, Mail, User, Newsletter } from "lucide-react";

interface FormFieldProps {
  label: string;
  name: string;
  type?: string;
  placeholder: string;
  required?: boolean;
  disabled?: boolean;
  errors?: string[];
  icon?: React.ReactNode;
  description?: string;
}

function FormField({
  label,
  name,
  type = "text",
  placeholder,
  required = false,
  disabled = false,
  errors = [],
  icon,
  description,
}: FormFieldProps) {
  const hasErrors = errors.length > 0;
  const fieldId = `field-${name}`;

  return (
    <div className="space-y-2">
      <Label htmlFor={fieldId} className="text-sm font-medium flex items-center gap-2">
        {icon}
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}

      <div className="relative">
        <Input
          id={fieldId}
          type={type}
          name={name}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className={`${hasErrors ? "border-red-500 focus:border-red-500" : ""} ${
            icon ? "pl-10" : ""
          }`}
          aria-describedby={hasErrors ? `${fieldId}-error` : undefined}
        />
        {icon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
            {icon}
          </div>
        )}
      </div>

      {hasErrors && (
        <div id={`${fieldId}-error`} className="space-y-1">
          {errors.map((error, index) => (
            <p key={index} className="text-sm text-red-600 flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {error}
            </p>
          ))}
        </div>
      )}
    </div>
  );
}

interface NewsletterFormProps {
  title?: string;
  description?: string;
  interests?: string[];
  className?: string;
  variant?: "card" | "inline";
}

export function NewsletterForm({
  title = "Stay Updated",
  description = "Subscribe to our newsletter for the latest insights, tips, and industry updates delivered straight to your inbox.",
  interests = ["Product Updates", "Industry News", "Tips & Tutorials", "Case Studies"],
  className = "",
  variant = "card",
}: NewsletterFormProps) {
  const [state, formAction, isPending] = useActionState<FormSubmissionResult | null, FormData>(
    submitNewsletterForm,
    null
  );

  const formRef = useRef<HTMLFormElement>(null);

  // Show toast when state changes
  useEffect(() => {
    if (state) {
      if (state.success) {
        toast.success("Welcome aboard!", {
          description: state.message,
          icon: <CheckCircle2 className="h-4 w-4" />,
        });
        // Reset form on success
        formRef.current?.reset();
      } else {
        // Only show toast for non-validation errors
        if (!state.errors) {
          toast.error("Error", {
            description: state.message,
            icon: <AlertCircle className="h-4 w-4" />,
          });
        }
      }
    }
  }, [state]);

  const FormContent = () => (
    <form ref={formRef} action={formAction} className="space-y-4">
      {/* Show general error message for non-field errors */}
      {state && !state.success && !state.errors && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{state.message}</AlertDescription>
        </Alert>
      )}

      {/* Show validation error summary */}
      {state?.errors && Object.keys(state.errors).length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please correct the errors below and try again.
          </AlertDescription>
        </Alert>
      )}

      {/* Success message */}
      {state?.success && (
        <Alert className="border-green-500 bg-green-50">
          <CheckCircle2 className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            {state.message}
            {state.timestamp && (
              <span className="block text-xs text-green-600 mt-1">
                Subscribed at {new Date(state.timestamp).toLocaleString()}
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <FormField
          label="Email Address"
          name="email"
          type="email"
          placeholder="<EMAIL>"
          required
          disabled={isPending}
          errors={state?.errors?.email}
          icon={<Mail className="h-4 w-4" />}
        />

        <FormField
          label="First Name"
          name="first_name"
          placeholder="John"
          disabled={isPending}
          errors={state?.errors?.firstName}
          icon={<User className="h-4 w-4" />}
          description="Optional - helps us personalize your experience"
        />

        {/* Interests */}
        {interests.length > 0 && (
          <div className="space-y-3">
            <Label className="text-sm font-medium">What interests you? (Optional)</Label>
            <div className="grid grid-cols-1 gap-3">
              {interests.map((interest) => (
                <div key={interest} className="flex items-center space-x-2">
                  <Checkbox
                    id={`interest-${interest}`}
                    name="interests"
                    value={interest}
                    disabled={isPending}
                  />
                  <Label
                    htmlFor={`interest-${interest}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {interest}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        <Button type="submit" disabled={isPending} className="w-full">
          {isPending ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Subscribing...
            </>
          ) : (
            <>
              <Newsletter className="h-4 w-4 mr-2" />
              Subscribe to Newsletter
            </>
          )}
        </Button>

        <p className="text-xs text-muted-foreground text-center">
          We respect your privacy. Unsubscribe at any time.
        </p>
      </div>
    </form>
  );

  if (variant === "inline") {
    return (
      <div className={className}>
        <div className="text-center mb-4">
          <h3 className="text-lg font-semibold flex items-center justify-center gap-2">
            <Newsletter className="h-5 w-5" />
            {title}
          </h3>
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        </div>
        <FormContent />
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <CardTitle className="text-xl font-bold flex items-center justify-center gap-2">
          <Newsletter className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>

      <CardContent>
        <FormContent />
      </CardContent>
    </Card>
  );
}
