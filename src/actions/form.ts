// actions/lead-actions.ts
"use server";

import { sendToN8nWebhook } from "@/lib/n8n-client";
import {
  contactFormSchema,
  leadFormSchema,
  newsletterSchema,
  type FormSubmissionResult,
  type N8NLeadPayload,
} from "@/lib/types";
import { headers } from "next/headers";

// Helper function to get client IP and user agent
async function getClientMetadata() {
  const headersList = await headers();
  const userAgent = headersList.get("user-agent") || "";
  const forwarded = headersList.get("x-forwarded-for");
  const realIp = headersList.get("x-real-ip");

  // Get IP address (handle various proxy headers)
  const ipAddress = forwarded?.split(",")[0]?.trim() || realIp || "unknown";

  return {
    userAgent,
    ipAddress,
    referrer: headersList.get("referer") || "",
  };
}

// Helper function to extract UTM parameters from URL or form data
function extractUtmParams(formData: FormData) {
  return {
    utmSource: formData.get("utm_source")?.toString(),
    utmMedium: formData.get("utm_medium")?.toString(),
    utmCampaign: formData.get("utm_campaign")?.toString(),
    utmContent: formData.get("utm_content")?.toString(),
    utmTerm: formData.get("utm_term")?.toString(),
  };
}

// Contact form submission (existing functionality enhanced)
export async function submitContactForm(
  prevState: FormSubmissionResult | null,
  formData: FormData
): Promise<FormSubmissionResult> {
  try {
    const clientMetadata = await getClientMetadata();
    const utmParams = extractUtmParams(formData);

    // Extract and validate form data
    const rawData = {
      name: formData.get("name"),
      email: formData.get("email"),
      message: formData.get("message"),
      company: formData.get("company") || undefined,
      jobTitle: formData.get("job_title") || undefined,
      phone: formData.get("phone") || undefined,
      website: formData.get("website") || undefined,
      ...utmParams,
    };

    // Validate with Zod
    const validationResult = contactFormSchema.safeParse(rawData);

    if (!validationResult.success) {
      console.error("❌ Validation failed:", validationResult.error.flatten());

      // Format validation errors for better UX
      const fieldErrors: Record<string, string[]> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        if (!fieldErrors[field]) {
          fieldErrors[field] = [];
        }
        fieldErrors[field].push(error.message);
      });

      return {
        success: false,
        message: "Please correct the errors below",
        errors: fieldErrors,
      };
    }

    const validatedData = validationResult.data;

    // Prepare N8N payload
    const n8nPayload: N8NLeadPayload = {
      email: validatedData.email,
      name: validatedData.name,
      message: validatedData.message,
      company: validatedData.company,
      jobTitle: validatedData.jobTitle,
      phone: validatedData.phone,
      website: validatedData.website,
      leadSource: "website_contact_form",
      utmSource: validatedData.utmSource,
      utmMedium: validatedData.utmMedium,
      utmCampaign: validatedData.utmCampaign,
      utmContent: validatedData.utmContent,
      utmTerm: validatedData.utmTerm,
      metadata: {
        source: "website",
        formType: "contact",
        userAgent: clientMetadata.userAgent,
        ipAddress: clientMetadata.ipAddress,
        referrer: clientMetadata.referrer,
        timestamp: Date.now(),
        sessionId: crypto.randomUUID(),
        pageUrl: formData.get("page_url")?.toString() || "",
      },
    };

    const result = await sendToN8nWebhook(n8nPayload, "/contact-form");

    return {
      success: true,
      message: result.message || "Thank you! We'll get back to you soon.",
      timestamp: result.timestamp,
      leadId: result.leadId,
      leadScore: result.leadScore,
      leadQuality: result.leadQuality,
    };
  } catch (error) {
    console.error("❌ Error submitting contact form:", error);
    return handleFormError(error);
  }
}

// Lead generation form submission (for landing pages)
export async function submitLeadForm(
  prevState: FormSubmissionResult | null,
  formData: FormData
): Promise<FormSubmissionResult> {
  try {
    const clientMetadata = await getClientMetadata();
    const utmParams = extractUtmParams(formData);

    const rawData = {
      firstName: formData.get("first_name"),
      lastName: formData.get("last_name"),
      email: formData.get("email"),
      company: formData.get("company"),
      jobTitle: formData.get("job_title") || undefined,
      phone: formData.get("phone") || undefined,
      website: formData.get("website") || undefined,
      leadMagnet: formData.get("lead_magnet") || undefined,
      interests: formData.getAll("interests") as string[],
    };

    const validationResult = leadFormSchema.safeParse(rawData);

    if (!validationResult.success) {
      const fieldErrors: Record<string, string[]> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        if (!fieldErrors[field]) {
          fieldErrors[field] = [];
        }
        fieldErrors[field].push(error.message);
      });

      return {
        success: false,
        message: "Please correct the errors below",
        errors: fieldErrors,
      };
    }

    const validatedData = validationResult.data;

    const n8nPayload: N8NLeadPayload = {
      email: validatedData.email,
      firstName: validatedData.firstName,
      lastName: validatedData.lastName,
      company: validatedData.company,
      jobTitle: validatedData.jobTitle,
      phone: validatedData.phone,
      website: validatedData.website,
      leadSource: "landing_page_form",
      leadMagnet: validatedData.leadMagnet,
      interests: validatedData.interests,
      ...utmParams,
      metadata: {
        source: "landing_page",
        formType: "lead",
        userAgent: clientMetadata.userAgent,
        ipAddress: clientMetadata.ipAddress,
        referrer: clientMetadata.referrer,
        timestamp: Date.now(),
        sessionId: crypto.randomUUID(),
        pageUrl: formData.get("page_url")?.toString() || "",
      },
    };

    const result = await sendToN8nWebhook(n8nPayload, "/webhook/lead-form");

    return {
      success: true,
      message:
        result.message || "Success! Check your email for the download link.",
      timestamp: result.timestamp,
      leadId: result.leadId,
      leadScore: result.leadScore,
      leadQuality: result.leadQuality,
    };
  } catch (error) {
    console.error("❌ Error submitting lead form:", error);
    return handleFormError(error);
  }
}

// Newsletter signup
export async function submitNewsletterForm(
  prevState: FormSubmissionResult | null,
  formData: FormData
): Promise<FormSubmissionResult> {
  try {
    const clientMetadata = await getClientMetadata();
    const utmParams = extractUtmParams(formData);

    const rawData = {
      email: formData.get("email"),
      firstName: formData.get("first_name") || undefined,
      interests: formData.getAll("interests") as string[],
    };

    const validationResult = newsletterSchema.safeParse(rawData);

    if (!validationResult.success) {
      const fieldErrors: Record<string, string[]> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        if (!fieldErrors[field]) {
          fieldErrors[field] = [];
        }
        fieldErrors[field].push(error.message);
      });

      return {
        success: false,
        message: "Please correct the errors below",
        errors: fieldErrors,
      };
    }

    const validatedData = validationResult.data;

    const n8nPayload: N8NLeadPayload = {
      email: validatedData.email,
      firstName: validatedData.firstName,
      leadSource: "newsletter_signup",
      interests: validatedData.interests,
      ...utmParams,
      metadata: {
        source: "newsletter",
        formType: "newsletter",
        userAgent: clientMetadata.userAgent,
        ipAddress: clientMetadata.ipAddress,
        referrer: clientMetadata.referrer,
        timestamp: Date.now(),
        sessionId: crypto.randomUUID(),
        pageUrl: formData.get("page_url")?.toString() || "",
      },
    };

    const result = await sendToN8nWebhook(n8nPayload, "/webhook/newsletter");

    return {
      success: true,
      message:
        result.message || "Welcome! You're now subscribed to our newsletter.",
      timestamp: result.timestamp,
      leadId: result.leadId,
      leadScore: result.leadScore,
      leadQuality: result.leadQuality,
    };
  } catch (error) {
    console.error("❌ Error submitting newsletter form:", error);
    return handleFormError(error);
  }
}

// Chat widget lead capture
export async function submitChatLead(leadData: {
  email: string;
  name?: string;
  message: string;
  phone?: string;
}): Promise<FormSubmissionResult> {
  try {
    const clientMetadata = await getClientMetadata();

    const n8nPayload: N8NLeadPayload = {
      email: leadData.email,
      name: leadData.name,
      message: leadData.message,
      phone: leadData.phone,
      leadSource: "chat_widget",
      metadata: {
        source: "chat",
        formType: "chat",
        userAgent: clientMetadata.userAgent,
        ipAddress: clientMetadata.ipAddress,
        referrer: clientMetadata.referrer,
        timestamp: Date.now(),
        sessionId: crypto.randomUUID(),
      },
    };

    const result = await sendToN8nWebhook(n8nPayload, "/webhook/chat-lead");

    return {
      success: true,
      message: result.message || "Thanks! We'll be in touch soon.",
      timestamp: result.timestamp,
      leadId: result.leadId,
      leadScore: result.leadScore,
      leadQuality: result.leadQuality,
    };
  } catch (error) {
    console.error("❌ Error submitting chat lead:", error);
    return handleFormError(error);
  }
}

// Error handler helper
function handleFormError(error: unknown): FormSubmissionResult {
  // Provide more specific error messages based on error type
  if (error instanceof TypeError && error.message.includes("fetch")) {
    return {
      success: false,
      message: "Cannot connect to server. Please ensure n8n is running.",
    };
  }

  if (error instanceof SyntaxError) {
    return {
      success: false,
      message: "Invalid response format from server. Please try again.",
    };
  }

  // Network timeout
  if (error instanceof Error && error.name === "AbortError") {
    return {
      success: false,
      message: "Request timed out. Please try again.",
    };
  }

  return {
    success: false,
    message: "An unexpected error occurred. Please try again.",
  };
}

export async function crawlWebsite(
  prevState: FormSubmissionResult | null,
  formData: FormData
): Promise<FormSubmissionResult> {
  try {
    console.log("📤 Submitting form to n8n...");

    // Extract and validate form data
    const rawData = {
      url: formData.get("url"),
    };

    console.log("📦 Raw form data:", rawData);

    // Make request to n8n webhook
    const response = await fetch(`http://localhost:5678/webhook/crawl`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({
        url: rawData.url,
      }),
    });

    console.log("📡 Response status:", response.status);

    if (!response.ok) {
      console.error(
        "❌ n8n webhook failed:",
        response.status,
        response.statusText
      );

      let errorMessage = `Server error: ${response.status}`;
      try {
        const errorData = await response.text();
        console.error("Error details:", errorData);

        switch (response.status) {
          case 404:
            errorMessage =
              "Webhook not found. Please check your n8n workflow is active.";
            break;
          case 422:
            errorMessage =
              "Invalid data sent to server. Please check your input.";
            break;
          case 500:
            errorMessage =
              "Server error. Please check your n8n workflow configuration.";
            break;
          case 503:
            errorMessage =
              "Service temporarily unavailable. Please try again later.";
            break;
          default:
            errorMessage = `Server error (${response.status}). Please try again.`;
        }
      } catch (e) {
        console.error("Could not parse error response:", e);
      }

      return {
        success: false,
        message: errorMessage,
      };
    }

    const data = await response.json();

    return {
      success: true,
      data,
      message: "Your url has been sent successfully!",
    };
  } catch (error) {
    console.error("❌ Error submitting form:", error);

    // Provide more specific error messages based on error type
    if (error instanceof TypeError && error.message.includes("fetch")) {
      return {
        success: false,
        message:
          "Cannot connect to server. Please ensure n8n is running on localhost:5678.",
      };
    }

    if (error instanceof SyntaxError) {
      return {
        success: false,
        message: "Invalid response format from server. Please try again.",
      };
    }

    return {
      success: false,
      message: "An unexpected error occurred. Please try again.",
    };
  }
}
