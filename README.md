# Vercel MCP Chat Application

A Next.js application that integrates Model Context Protocol (MCP) with AI chat functionality using OpenRouter and the Vercel MCP Adapter.

## Features

- 🤖 AI Chat powered by OpenRouter
- 🔧 Model Context Protocol (MCP) integration
- 🎲 Built-in dice rolling tool via MCP
- 🚀 Production-ready deployment configuration
- ⚡ Real-time streaming responses
- 🔒 Environment validation and security

## Architecture

- **Frontend**: Next.js 15 with React 19
- **AI Provider**: OpenRouter (configurable models)
- **MCP Server**: Vercel MCP Adapter with Redis
- **Transport**: Server-Sent Events (SSE)
- **Deployment**: Vercel Platform

## Prerequisites

- Node.js 18+
- Redis server (for MCP pub/sub messaging)
- OpenRouter API key

## Environment Variables

Create a `.env.local` file with the following variables:

```bash
# Required
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Optional (with defaults)
MODEL_NAME=deepseek/deepseek-r1-0528-qwen3-8b:free
REDIS_URL=redis://localhost:6379
NODE_ENV=development
```

### Getting API Keys

1. **OpenRouter API Key**:
   - Sign up at [OpenRouter](https://openrouter.ai/)
   - Go to API Keys section
   - Create a new API key

## Local Development

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd vercel-mcp
npm install
```

2. **Start Redis server**:
```bash
# Using Homebrew (macOS)
brew install redis
redis-server

# Using Docker
docker run -d -p 6379:6379 redis:alpine

# Using Redis Cloud (production)
# Set REDIS_URL to your cloud Redis connection string
```

3. **Set up environment variables**:
```bash
cp .env.example .env.local
# Edit .env.local with your API keys
```

4. **Run the development server**:
```bash
npm run dev
```

5. **Open [http://localhost:3000](http://localhost:3000)** in your browser

## Production Deployment

### Deploy to Vercel

1. **Push to GitHub/GitLab/Bitbucket**

2. **Connect to Vercel**:
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Import your repository
   - Configure environment variables

3. **Set Environment Variables in Vercel**:
   ```
   OPENROUTER_API_KEY=your_api_key
   REDIS_URL=your_production_redis_url
   MODEL_NAME=your_preferred_model (optional)
   ```

4. **Deploy**:
   ```bash
   npm run deploy  # Validates and builds
   ```

### Redis Setup for Production

**Option 1: Redis Cloud (Recommended)**
- Sign up at [Redis Cloud](https://redis.com/try-free/)
- Create a database
- Copy the connection string to `REDIS_URL`

**Option 2: Railway**
- Deploy Redis on [Railway](https://railway.app/)
- Use the provided connection string

**Option 3: Upstash**
- Use [Upstash Redis](https://upstash.com/)
- Serverless Redis with generous free tier

## API Endpoints

- `POST /api/chat` - Main chat endpoint
- `GET /api/health` - Health check endpoint
- `GET /api/mcp/sse` - MCP Server-Sent Events endpoint
- `POST /api/mcp/message` - MCP message handling

## MCP Tools

The application includes a built-in dice rolling tool:

```typescript
// Example usage in chat
"Roll a 6-sided die"
"Roll a 20-sided die"
```

### Adding Custom MCP Tools

Edit `src/app/api/mcp/[transport]/route.ts`:

```typescript
server.tool(
  "your_tool_name",
  "Tool description",
  { param: z.string() }, // Zod schema for parameters
  async ({ param }) => {
    // Tool implementation
    return {
      content: [{ type: "text", text: "Tool result" }],
    };
  }
);
```

## Monitoring and Health Checks

- **Health Check**: `GET /api/health`
- **Logs**: Check Vercel Function logs
- **Redis**: Monitor Redis connection and memory usage

## Troubleshooting

### Common Issues

1. **"405 Method Not Allowed"**
   - Ensure Redis is running
   - Check REDIS_URL environment variable

2. **"Environment validation failed"**
   - Verify all required environment variables are set
   - Check API key format

3. **MCP Connection Issues**
   - Verify Redis connectivity
   - Check network configuration
   - Review server logs

### Development Tips

```bash
# Type checking
npm run type-check

# Linting
npm run lint

# Full validation
npm run validate
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run `npm run validate`
5. Submit a pull request

## License

MIT License - see LICENSE file for details
